import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/services/permission_service.dart';
import '../../../../core/utils/log_service.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/loading_overlay.dart';
import '../../../../shared/widgets/permission_dialog.dart';
import '../providers/ai_generation_providers.dart';
import '../widgets/image_upload_card.dart';

/// 图片上传页面
@RoutePage()
class ImageUploadScreen extends ConsumerStatefulWidget {
  const ImageUploadScreen({super.key});

  @override
  ConsumerState<ImageUploadScreen> createState() => _ImageUploadScreenState();
}

class _ImageUploadScreenState extends ConsumerState<ImageUploadScreen> {
  final LogService _logger = LogService();
  final ImagePicker _picker = ImagePicker();
  final PermissionService _permissionService = PermissionService();
  List<String> _selectedImages = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _logger.i('图片上传页面初始化');
    _checkInitialPermissions();
  }

  /// 检查初始权限状态
  Future<void> _checkInitialPermissions() async {
    _logger.d('检查初始权限状态');

    // 检查通知权限
    final notificationStatus = await _permissionService.checkNotificationPermission();
    if (notificationStatus == PermissionResult.denied) {
      _requestNotificationPermission();
    }
  }

  /// 请求通知权限
  Future<void> _requestNotificationPermission() async {
    final result = await PermissionDialog.showNotificationPermission(context);
    if (result == true) {
      final status = await _permissionService.requestNotificationPermission();
      _logger.i('通知权限请求结果: ${status.name}');
    }
  }

  @override
  Widget build(BuildContext context) {
    // final aiGenerationState = ref.watch(aiGenerationProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('上传照片'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInstructions(),
              SizedBox(height: 24.h),
              _buildImageGrid(),
              SizedBox(height: 24.h),
              _buildUploadButtons(),
              const Spacer(),
              _buildContinueButton(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建说明文本
  Widget _buildInstructions() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                '上传要求',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            '• 支持JPG、PNG、HEIC格式\n'
            '• 单张图片不超过10MB\n'
            '• 建议上传1-3张清晰的正面照\n'
            '• 确保人脸清晰可见，光线充足',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图片网格
  Widget _buildImageGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '已选择照片 (${_selectedImages.length}/3)',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        SizedBox(
          height: 120.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _selectedImages.length + (_selectedImages.length < 3 ? 1 : 0),
            itemBuilder: (context, index) {
              if (index < _selectedImages.length) {
                return ImageUploadCard(
                  imagePath: _selectedImages[index],
                  onRemove: () => _removeImage(index),
                );
              } else {
                return _buildAddImageCard();
              }
            },
          ),
        ),
      ],
    );
  }

  /// 构建添加图片卡片
  Widget _buildAddImageCard() {
    return Container(
      width: 120.w,
      margin: EdgeInsets.only(right: 12.w),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300, width: 2),
        borderRadius: BorderRadius.circular(12.r),
        color: Colors.grey.shade50,
      ),
      child: InkWell(
        onTap: _showImageSourceDialog,
        borderRadius: BorderRadius.circular(12.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 32.sp,
              color: Colors.grey.shade600,
            ),
            SizedBox(height: 8.h),
            Text(
              '添加照片',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建上传按钮
  Widget _buildUploadButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '从相册选择',
            onPressed: () => _pickImages(ImageSource.gallery),
            backgroundColor: Colors.blue.shade50,
            textColor: Colors.blue.shade700,
            icon: Icons.photo_library_outlined,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: CustomButton(
            text: '拍照',
            onPressed: () => _pickImages(ImageSource.camera),
            backgroundColor: Colors.green.shade50,
            textColor: Colors.green.shade700,
            icon: Icons.camera_alt_outlined,
          ),
        ),
      ],
    );
  }

  /// 构建继续按钮
  Widget _buildContinueButton() {
    return CustomButton(
      text: '继续选择风格',
      onPressed: _selectedImages.isNotEmpty ? _continueToStyleSelection : null,
      isFullWidth: true,
    );
  }

  /// 显示图片来源选择对话框
  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library_outlined),
              title: const Text('从相册选择'),
              onTap: () {
                Navigator.pop(context);
                _pickImages(ImageSource.gallery);
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt_outlined),
              title: const Text('拍照'),
              onTap: () {
                Navigator.pop(context);
                _pickImages(ImageSource.camera);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 选择图片
  Future<void> _pickImages(ImageSource source) async {
    _logger.d('开始选择图片: ${source.name}');

    try {
      // 检查权限
      if (!await _checkPermissions(source)) {
        _logger.w('权限检查失败');
        return;
      }

      setState(() => _isLoading = true);

      if (source == ImageSource.gallery) {
        _logger.d('从相册选择多张图片');
        final images = await _picker.pickMultiImage(
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 85,
        );

        if (images.isNotEmpty) {
          _logger.i('从相册选择了${images.length}张图片');
          _addImages(images.map((image) => image.path).toList());
        } else {
          _logger.d('用户取消了图片选择');
        }
      } else {
        _logger.d('使用相机拍照');
        final image = await _picker.pickImage(
          source: source,
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 85,
        );

        if (image != null) {
          _logger.i('拍摄了一张照片: ${image.path}');
          _addImages([image.path]);
        } else {
          _logger.d('用户取消了拍照');
        }
      }
    } catch (e, stackTrace) {
      _logger.e('选择图片失败', e, stackTrace);
      String errorMessage = '选择图片失败';
      if (e.toString().contains('camera_access_denied')) {
        errorMessage = '相机权限被拒绝，请在设置中开启';
      } else if (e.toString().contains('photo_access_denied')) {
        errorMessage = '相册权限被拒绝，请在设置中开启';
      }
      _showErrorSnackBar(errorMessage);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 检查权限
  Future<bool> _checkPermissions(ImageSource source) async {
    try {
      if (source == ImageSource.camera) {
        return await _checkCameraPermission();
      } else {
        return await _checkPhotosPermission();
      }
    } catch (e) {
      _logger.e('权限检查失败: $e');
      _showErrorSnackBar('权限检查失败，请在设置中手动开启权限');
      return false;
    }
  }

  /// 检查相机权限
  Future<bool> _checkCameraPermission() async {
    final status = await _permissionService.checkCameraPermission();

    switch (status) {
      case PermissionResult.granted:
        return true;

      case PermissionResult.denied:
        final dialogResult = await PermissionDialog.showCameraPermission(context);
        if (dialogResult == true) {
          final requestResult = await _permissionService.requestCameraPermission();
          if (requestResult == PermissionResult.granted) {
            return true;
          } else if (requestResult == PermissionResult.permanentlyDenied) {
            await _showPermanentlyDeniedDialog(PermissionType.camera);
          }
        }
        return false;

      case PermissionResult.permanentlyDenied:
        await _showPermanentlyDeniedDialog(PermissionType.camera);
        return false;

      default:
        _showErrorSnackBar('相机权限受限，无法使用拍照功能');
        return false;
    }
  }

  /// 检查相册权限
  Future<bool> _checkPhotosPermission() async {
    try {
      // 先检查当前权限状态
      final currentStatus = await _permissionService.checkPhotosPermission();
      
      // 如果已经有权限，直接返回
      if (currentStatus == PermissionResult.granted || 
          currentStatus == PermissionResult.limited) {
        _logger.d('相册权限已获得: ${currentStatus.name}');
        return true;
      }
      
      // 如果权限被永久拒绝，直接显示设置引导
      if (currentStatus == PermissionResult.permanentlyDenied) {
        _logger.w('相册权限被永久拒绝');
        await _showPermanentlyDeniedDialog(PermissionType.photos);
        return false;
      }
      
      // 显示权限申请对话框
      final shouldRequest = await PermissionDialog.showPhotosPermissionWithContext(
        context,
        contextDescription: '您即将选择照片来生成AI婚纱照。为了让您能够浏览和选择相册中的照片，我们需要获得相册访问权限。',
      );
      
      if (shouldRequest != true) {
        _logger.d('用户拒绝授权相册权限');
        return false;
      }
      
      // 请求权限
      final result = await _permissionService.requestPhotosPermission();
      
      switch (result) {
        case PermissionResult.granted:
        case PermissionResult.limited:
          _logger.i('相册权限申请成功: ${result.name}');
          return true;
          
        case PermissionResult.denied:
          _logger.w('相册权限申请被拒绝');
          _showErrorSnackBar('需要相册权限才能选择照片，请重新尝试');
          return false;
          
        case PermissionResult.permanentlyDenied:
          _logger.w('相册权限被永久拒绝');
          await _showPermanentlyDeniedDialog(PermissionType.photos);
          return false;
          
        case PermissionResult.restricted:
          _logger.w('相册权限受限');
          _showErrorSnackBar('设备限制无法访问相册');
          return false;
      }
    } catch (e) {
      _logger.e('检查相册权限失败: $e');
      _showErrorSnackBar('权限检查失败，请稍后重试');
      return false;
    }
  }

  /// 显示权限被永久拒绝的对话框
  Future<void> _showPermanentlyDeniedDialog(PermissionType type) async {
    final permissionName = type == PermissionType.camera ? '相机' : '相册';
    final result = await PermissionDialog.showPermanentlyDeniedDialog(
      context,
      type,
      permissionName,
    );

    if (result == true) {
      await _permissionService.openSettings();
    }
  }

  /// 添加图片
  void _addImages(List<String> imagePaths) {
    final remainingSlots = 3 - _selectedImages.length;
    final imagesToAdd = imagePaths.take(remainingSlots).toList();
    
    setState(() {
      _selectedImages.addAll(imagesToAdd);
    });
    
    _logger.i('添加了${imagesToAdd.length}张图片');
    
    if (imagePaths.length > remainingSlots) {
      _showErrorSnackBar('最多只能选择3张图片');
    }
  }

  /// 移除图片
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
    _logger.d('移除了第${index + 1}张图片');
  }

  /// 继续到风格选择
  void _continueToStyleSelection() {
    _logger.i('继续到风格选择页面');
    // TODO: 导航到风格选择页面
    // context.router.push(StyleSelectionRoute(imagePaths: _selectedImages));
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
